import { REPORT_SECTIONS } from "../../config/constants";
import { ReportType } from "../../global";
import { useLocation } from "react-router";
// Hooks para renderizar as seções
import { useRenderDadosPessoais } from "./NEW_renderDadosPessoais.strategy";
import { useRenderEmails } from "./NEW_renderEmails.strategy";
import { useRenderEnderecos } from "./NEW_renderEnderecos.strategy";
import { useRenderDiariosOficiais } from "./NEW_renderDiariosOficiais.strategy";
import { useRenderTelefonesArray } from "./NEW_renderTelefones.strategy";
import { useRenderPossiveisContas } from "./NEW_renderPossiveisContas.strategy";
import { useRenderSocios } from "./NEW_renderSocios.strategy"
import { useRenderParentes } from "./NEW_renderParentes.strategy";

export const useNewStrategyMap = () => {
  const location = useLocation();
  const reportType: ReportType | string = location.pathname.split("/")[2];

  // CPF
  const cpfStrategyMap: Record<string, any> = {
    [REPORT_SECTIONS.dados_pessoais]: useRenderDadosPessoais(REPORT_SECTIONS.dados_pessoais),
    [REPORT_SECTIONS.telefones]: useRenderTelefonesArray(REPORT_SECTIONS.telefones),
    [REPORT_SECTIONS.emails]: useRenderEmails(REPORT_SECTIONS.emails),
    [REPORT_SECTIONS.enderecos]: useRenderEnderecos(REPORT_SECTIONS.enderecos),
    [REPORT_SECTIONS.diarios_oficiais_cpf]: useRenderDiariosOficiais(REPORT_SECTIONS.diarios_oficiais_cpf),
    [REPORT_SECTIONS.diarios_oficiais_nome]: useRenderDiariosOficiais(REPORT_SECTIONS.diarios_oficiais_nome),
    [REPORT_SECTIONS.possiveis_contas_em_sites]: useRenderPossiveisContas(REPORT_SECTIONS.possiveis_contas_em_sites),
    [REPORT_SECTIONS.socios]: useRenderSocios(REPORT_SECTIONS.socios),
    [REPORT_SECTIONS.parentes]: useRenderParentes(REPORT_SECTIONS.parentes)
  };

  const cnpjStrategyMap: Record<string, any> = {
    [REPORT_SECTIONS.dados_empresa]: useRenderDadosPessoais(REPORT_SECTIONS.dados_empresa),
    [REPORT_SECTIONS.telefones]: useRenderTelefonesArray(REPORT_SECTIONS.telefones),
    [REPORT_SECTIONS.emails]: useRenderEmails(REPORT_SECTIONS.emails),
    [REPORT_SECTIONS.enderecos]: useRenderEnderecos(REPORT_SECTIONS.enderecos),
    [REPORT_SECTIONS.diarios_oficiais_cnpj]: useRenderDiariosOficiais(REPORT_SECTIONS.diarios_oficiais_cnpj),
    [REPORT_SECTIONS.socios]: useRenderSocios(REPORT_SECTIONS.socios)
  };

  switch (reportType) {
    case "cnpj":
      return cnpjStrategyMap;
    case "telefone":
      return cpfStrategyMap;
    case "cpf":
    default:
      return cpfStrategyMap;
  }
};