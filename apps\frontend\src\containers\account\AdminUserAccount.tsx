import { Text } from "@snap/design-system";
import { Column, DataTable } from "~/components/Table";
import AccountToolbar from "./AccountToolbar";
import AccountDateFilter from "./AccountDateFilter";
import AccountToolbarUsers from "./AccountToolbarUsers";
import { USER_CONSTANTS } from "~/helpers/constants";
import { Delete, Trash, Trash2 } from "lucide-react";
import { BsChatSquareText } from "react-icons/bs";

export interface UserConfigDialogContentProps {
  onEntryTypeChange?: (value: string) => void;
  onInputChange?: (e: React.ChangeEvent<HTMLInputElement>) => void;
  onFolderSelect?: () => void;
  description?: string;
  defaultEntryType?: string;
  defaultInputValue?: string;
}

const columnProps = {
  name: USER_CONSTANTS.user_data.name,
  account_type: USER_CONSTANTS.user_data.account_type,
  invite_status: USER_CONSTANTS.user_data.invite_status,
}

const userColumns: Column[] = [
  {
    key: columnProps.name,
    header: "Nome do Usuário",
    render: (_, row) => (
      <div className="flex items-center gap-4">
        <Text variant="body-md" className="font-semibold">{row[columnProps.name]}</Text>
      </div>
    ),
  },
  {
    key: columnProps.account_type,
    header: "Perfil",
    render: (_, row) => (
      <div className="flex items-center gap-4">
        <Text variant="body-md" className="font-semibold">{row[columnProps.account_type]}</Text>
      </div>
    ),
  },
  {
    key: columnProps.invite_status,
    header: "Status",
    render: (_, row) => (
      <div className="flex items-center gap-4">
        <Text variant="body-md" className="font-semibold">{row[columnProps.invite_status]}</Text>
      </div>
    ),
  }
]

const mockLogUserRows = [
  { id: 1, name: "Andreia Souza", account_type: "administrador", invite_status: "cadastrado" },
  { id: 2, name: "João Silva", account_type: "investigador", invite_status: "convite enviado" },
  { id: 3, name: "Maria Santos", account_type: "investigador", invite_status: "convite enviado" },
  { id: 4, name: "Pedro Oliveira", account_type: "investigador", invite_status: "convite enviado" },
  { id: 5, name: "Ana Rodrigues", account_type: "investigador", invite_status: "convite enviado" },
  { id: 6, name: "Carlos Pereira", account_type: "investigador", invite_status: "convite enviado" },
  { id: 7, name: "Lucas Costa", account_type: "investigador", invite_status: "convite enviado" },
  { id: 8, name: "Fernanda Almeida", account_type: "investigador", invite_status: "convite enviado" },
  { id: 9, name: "Rafaela Sousa", account_type: "investigador", invite_status: "convite enviado" },
  { id: 10, name: "Gustavo Lima", account_type: "investigador", invite_status: "convite enviado" },
  { id: 11, name: "Isabela Rocha", account_type: "investigador", invite_status: "convite enviado" },
  { id: 12, name: "Bruno Castro", account_type: "investigador", invite_status: "convite enviado" },
  { id: 13, name: "Mariana Mendes", account_type: "investigador", invite_status: "convite enviado" },
  { id: 14, name: "Diego Santos", account_type: "investigador", invite_status: "convite enviado" },
  { id: 15, name: "Carolina Rodrigues", account_type: "investigador", invite_status: "convite enviado" },
  { id: 16, name: "Rafael Almeida", account_type: "investigador", invite_status: "convite enviado" },
  { id: 17, name: "Bruna Oliveira", account_type: "investigador", invite_status: "convite enviado" },
  { id: 18, name: "Diego Santos", account_type: "investigador", invite_status: "convite enviado" },
  { id: 19, name: "Carolina Rodrigues", account_type: "investigador", invite_status: "convite enviado" },
  { id: 20, name: "Rafael Almeida", account_type: "investigador", invite_status: "convite enviado" }
]

const userActions = [
  {
    label: "Excluir",
    onClick: (row: typeof mockLogUserRows[0]) => console.log(row),
    icon: <Trash2 size={16} />
  },
  {
    label: "Enviar Mensagem",
    onClick: (row: typeof mockLogUserRows[0]) => console.log(row),
    icon: <BsChatSquareText size={16} />
  },
]

const AdminUserAccount = ({ }: UserConfigDialogContentProps) => {

  return (
    <div className="flex flex-col flex-4/5">

      <AccountToolbarUsers />
      <div className="overflow-y-auto overflow-x-hidden min-h-0 max-h-[calc(100vh-386px)]">
        <DataTable
          columns={userColumns}
          data={mockLogUserRows}
          enableSelection
          actions={userActions}
          pagination={{
            pageSize: 8,
            totalItems: mockLogUserRows.length,
            currentPage: 1,
            onPageChange: (page) => console.log(page),
          }}
        />
      </div>

    </div>
  );
}

export default AdminUserAccount;