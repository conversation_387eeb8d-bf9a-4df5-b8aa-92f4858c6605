import { Dialog } from '~/components/Dialog';
import {useDialogActions, useDialogProps, useIsDialogIsOpen  } from '~/store/dialogStore';

export function DialogContainer() {
  const { closeDialog } = useDialogActions();
  const isOpen = useIsDialogIsOpen();
  const dialogProps = useDialogProps();

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && closeDialog()}>
      <Dialog.Content className="bg-modal-body">
        {dialogProps?.title && (
          <Dialog.Header>
            <Dialog.Title>
              {dialogProps?.title || "Dialog Title"}
            </Dialog.Title>
            <Dialog.Icon>{dialogProps?.icon}</Dialog.Icon>
          </Dialog.Header>
        )}
        <Dialog.Body>
          <Dialog.Description>
            {dialogProps?.description}
          </Dialog.Description>
          {dialogProps?.content}
        </Dialog.Body>
        {dialogProps?.footer && (
          <Dialog.Footer>{dialogProps.footer}</Dialog.Footer>
        )}
      </Dialog.Content>
    </Dialog>
  );
}
