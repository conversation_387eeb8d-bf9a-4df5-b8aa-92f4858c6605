import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import {
  fetchReportList,
  fetchReportById,
  createNewReport,
  addNewReport,
} from "~/services/gateways/report.gateway";
import { useEncryption } from "~/hooks/useEncryption";
import type {
  ReportData,
} from "~/types/global";
import { toast } from "sonner";
import { useDialogActions } from "~/store/dialogStore";
import { useNewPendingReportsActions } from "~/store/newReportStatusStore";
import {
  decryptReportPayload,
  encryptReportPayload,
} from "~/helpers/encryption.helper";
import { REPORT_CONSTANTS, tanstackQueryConfig } from "~/helpers/constants";
import { useReportListLoadCount, useReportListPage, useReportListActions, useReportListSearchFilter } from "~/store/reportListStore";
import { useUserIsVerified } from "~/store/userStore";
import { getCurrentISODateWithMicroseconds } from "~/helpers";

export const useReportCRUD = () => {
  const { closeDialog } = useDialogActions();
  const queryClient = useQueryClient();
  const { decryptData, encryptData, encryptNgrams } = useEncryption();
  const { clearPendingList } = useNewPendingReportsActions();
  const isVerified = useUserIsVerified();
  const pageNumber = useReportListPage();
  const limitNumber = useReportListLoadCount()
  const searchFilter = useReportListSearchFilter()
  const { setReportList, incrementReportList, resetPagination, setLoadMore, clearSearchFilter } = useReportListActions()

  const reportListQuery = useQuery({
    queryKey: ["reports", "list"],
    queryFn: async ({ meta }) => {
      try {
        console.log(isVerified, "isVerified");
        clearPendingList();

        let hmacFilters
        // Se houver valor no campo de busca, transformar em n-grams e criptografar para usar de filtro
        if (searchFilter) {
          console.log("reportListQuery searchFilter", searchFilter);
          const ngrams = await encryptNgrams({
            searched_value: searchFilter,
          });
          hmacFilters = ngrams.searched_value
          //clearSearchFilter()
        }

        const reportsListData = await fetchReportList({
          page: pageNumber,
          limit: limitNumber,
          hmacFilters
        });

        const decryptedReportList = await Promise.all(
          reportsListData.map(async (report, index) => {
            try {
              const decrypted = await decryptReportPayload(report, decryptData);
              return decrypted;
            } catch (err: any) {
              throw new Error(
                `Erro ao descriptografar o relatório #${index + 1}: ${err?.message
                }`
              );
            }
          })
        );
        //@ts-ignore
        pageNumber === 1 ? setReportList(decryptedReportList) : incrementReportList(decryptedReportList)
        const itemCount = decryptedReportList.length
        const hasMore = Boolean(itemCount === limitNumber)
        setLoadMore(hasMore)
        console.log("decryptedReportList", decryptedReportList);

        // TODO - remover caso não esteja agregando NADA - teste para evitar mensagem de refetch sempre que chamar a lista
        const isScheduledRefetch = meta?.fetchReason === "refetch";
        if (isScheduledRefetch) {
          sessionStorage.removeItem("reports_list_toast_shown");
        }

        return {
          data: decryptedReportList as ReportData[],
          isScheduledRefetch: isScheduledRefetch,
        };
      } catch (err: any) {
        console.error("Error fetching report list:", err);
        toast.error("Erro", {
          description: "Ocorreu um erro ao tentar atualizar a lista.",
        });
        return {
          data: [],
          isScheduledRefetch: false,
        }; // TODO - organizar errors (retornos e componentes)
      }
    },
    ...tanstackQueryConfig,
    staleTime: Infinity,
    refetchInterval: 1000 * 60 * 60, // refetch em 1h para o caso do usuário estar idle e o access token expirado
    enabled: isVerified,
    meta: {
      fetchReason: "initial",
    },
  });

  const reportDetailsQuery = (id: string) =>
    useQuery<ReportData, Error>({
      queryKey: ["reports", "details", id],
      queryFn: async () => {
        const response = await fetchReportById(id);
        const decrypted = await decryptReportPayload(response as ReportData, decryptData);
        console.log("[useReportCRUD] reportDetailsQuery decrypted", decrypted);
        return decrypted as ReportData;
      },
      staleTime: Infinity,
      enabled: !!id && isVerified,
    });

  const newReportMutation = useMutation({
    mutationFn: createNewReport,
    onSuccess: async () => {
      await invalidateToInitialPageNoFilters()
      closeDialog();

      toast.success("Gerando novo relatório...");
      window.dispatchEvent(new Event("snap:report-created"));
    },

    onError: (errPayload: unknown) => {
      const detail = (errPayload as any).detail;
      const errorTitle = "Erro ao criar relatório";
      const errorMessage = detail?.message ?? "Ocorreu um erro ao tentar criar um novo relatório";
      console.error(`${errorTitle}: `, errorMessage);

      toast.error("Ocorreu um erro ao tentar criar um novo relatório");
    },
  });

  const addNewReportMutation = useMutation({
    mutationFn: async (report: any) => {
      try {
        const { user_reports_id } = report;
        if (user_reports_id) {
          const ngrams = await encryptNgrams({
            report_name: report.report_name,
            report_search_args: report.report_search_args,
            subject_name: report.subject_name,
            subject_mother_name: report.subject_mother_name,
          });

          const modifiedReports = {
            ...report,
            [REPORT_CONSTANTS.new_report.report_status]: REPORT_CONSTANTS.status.completed,
            [REPORT_CONSTANTS.new_report.modified_at]: getCurrentISODateWithMicroseconds(),
          };

          const encryptedReport = await encryptReportPayload(
            modifiedReports,
            encryptData
          );

          const payload = {
            ...encryptedReport,
            [REPORT_CONSTANTS.new_report.hmac]: { [REPORT_CONSTANTS.new_report.ngrams]: ngrams } // formato exigido pelo backend
          };

          console.log("[useReportCRUD] addNewReportMutation payload", payload);
          return addNewReport(payload, user_reports_id);
        }
      } catch (error) {
        console.error("Error encrypting report:", error);
        throw new Error("Failed to encrypt report data");
      }
    },
  });

  const invalidateReports = async () => {
    await queryClient.invalidateQueries({
      queryKey: ["reports", "list"],
      exact: false,
    });
  };

  // TODO - refatorar para não precisar usar diferentes funções para invalidar a lista de relatórios
  const invalidateToInitialPage = async () => {
    await resetPagination();
    await queryClient.invalidateQueries({
      queryKey: ["reports", "list"],
      exact: false,
    });
  };

  const invalidateToInitialPageNoFilters = async () => {
    await resetPagination();
    await clearSearchFilter();
    await queryClient.invalidateQueries({
      queryKey: ["reports", "list"],
      exact: false,
    });
  };

  const invalidateReportDetails = async (id: string) => {
    await queryClient.invalidateQueries({
      queryKey: ["reports", "details", id],
      exact: true,
    });
  };

  return {
    reportListQuery,
    reportDetailsQuery,
    invalidateReports,
    invalidateToInitialPage,
    invalidateToInitialPageNoFilters,
    invalidateReportDetails,
    newReportMutation,
    addNewReportMutation,
  };
};
