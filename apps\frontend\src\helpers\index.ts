import type { NormalizeOptions } from "~/types/global";
import { Dictionary, ListItemProps } from "~/types/global";
import labels from "~constants/labels.json";
import React from "react";
import { BsFilePersonFill, BsBuilding } from "react-icons/bs";
import { <PERSON>dLocalPhone, MdEmail } from "react-icons/md";
import { TbCirclesRelation } from "react-icons/tb";
import { PARSED_VALUES, TRANSLATED_LABELS } from "./constants";

/**
 * Advanced string normalizer with configurable options for special character handling
 * and case transformation. Safely processes strings while handling invalid inputs.
 *
 * @param value - Input value to normalize (any type, string processing only)
 * @param options - Configuration options for normalization behavior
 *
 * @returns Normalized string cleaned of diacritics and special characters,
 *          or empty string for invalid inputs
 *
 * @example
 * // Basic usage
 * normalizeStringAdvanced("Nĭñja_Çõmpâny"); // "Ninja_Company"
 *
 * // With options
 * normalizeStringAdvanced("Relações_2024!", {
 *   case: 'lower',
 *   allowSpecialChars: /[^a-zA-Z0-9_-]/g
 * }); // "relacoes_2024"
 */
export const normalizeString = (
  value: unknown,
  options: NormalizeOptions = {}
): string => {
  if (typeof value !== "string") return "";

  let normalized = value.normalize("NFD").replace(/[\u0300-\u036f]/g, "");

  if (options.allowSpecialChars) {
    normalized = normalized.replace(options.allowSpecialChars, "");
  }

  switch (options.case) {
    case "lower":
      return normalized.toLowerCase();
    case "upper":
      return normalized.toUpperCase();
    default:
      return normalized;
  }
};

export const isEmptyObject = (args: any) => {
  return JSON.stringify(args) === "{}";
};

export const isObject = (value: unknown): value is Record<string, unknown> => {
  return value !== null && typeof value === "object";
};

export const getSingularWord: Dictionary = {
  aplicativos: "aplicativo",
  enderecos: "endereço",
  veiculos: "veículo",
  diarios: "diário",
  oficiais: "oficial",
  sources: "fonte",
  socios: "sócio",
  movimentacoes: "movimentação",
  emails: "email",
  telefones: "telefone",
  pessoas: "pessoa",
  empresas: "empresa",
  processos: "processo",
};

export const parseKeyToLabel = (key: string): string => {
  return key
    .split("_")
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
    .join(" ");
};

export const getSingular = (key: string): string => {
  if (!key) return "";
  const normalizedkey = normalizeString(key, { case: "lower" });
  return getSingularWord[normalizedkey] || key;
};

/**
 * Searches an array for an object with a `bookmark` property equal to the provided value.
 * Defaults to searching for bookmark === 4.
 *
 * @param data - An array of objects to search.
 * @param bookmarkValue - The bookmark value to match (default is 4)
 * @returns The first object with the matching bookmark or undefined if not found.
 */
export function findItemWithBookmark<T = any>(
  data: any[],
  bookmarkValue: number = 4
): T | undefined {
  if (!Array.isArray(data)) {
    throw new Error("findItemWithBookmark: Expected an array as input");
  }

  return data.find(
    (item: any) =>
      item &&
      typeof item === "object" &&
      "bookmark" in item &&
      item.bookmark === bookmarkValue
  );
}

/**
 * Retrieves a nested value from an object using a path array.
 * @param obj - The object to traverse.
 * @param pathArray - An array of keys/indices to navigate.
 * @returns The nested value if found, otherwise undefined.
 */
export const getValueAtPath = (
  obj: any,
  pathArray: (string | number)[]
): any => {
  if (!obj || !Array.isArray(pathArray)) return undefined;
  if (pathArray.length === 0) return obj;

  const result = pathArray.reduce(
    (acc, key) => (acc && acc[key] !== undefined ? acc[key] : undefined),
    obj
  );

  return result;
};

/**
 * Procura dentro de um array de objetos um item que contenha
 * "mae" (caso-insensitivo) no valor da propriedade "label default key".
 * Se encontrar, retorna um objeto { nome_da_mae: "Nome da Mãe" }.
 * Se não encontrar, retorna undefined.
 */
export function findPersonMother(
  personArray: any[]
): { nome_da_mae: string } | undefined {
  if (!Array.isArray(personArray)) return undefined;

  const mother = personArray.find((item: any) => {
    const label = (item["label default key"] || "").toLowerCase();
    return label.includes("mae");
  });

  if (mother && (mother["full name"] || mother.full_name)) {
    return { nome_da_mae: mother["full name"] || mother.full_name };
  }

  return undefined;
}

/**
 * Filtra um array de objetos procurando por relações de parentesco específicas.
 * Se existir um objeto com `bookmark === 4`, busca relacionamentos dentro da sua propriedade "pessoa".
 * Se houver duplicatas (mesmo `label default key`), mantém o objeto com mais propriedades.
 *
 * @param personArray - O array de objetos contendo informações das pessoas.
 * @param relationships - Um array de strings contendo os graus de parentesco desejados.
 * @param returnMatching - Se true, retorna objetos com a relação; se false, retorna os que não possuem a relação.
 * @returns Um array filtrado contendo os objetos que correspondem (ou não) aos relacionamentos fornecidos.
 */
export function findPeopleByRelationship(
  personArray: any[],
  relationships: string[],
  returnMatching: boolean = true
): any[] {
  if (
    !Array.isArray(personArray) ||
    !Array.isArray(relationships) ||
    relationships.length === 0
  ) {
    return [];
  }

  // Converte todas as strings de relacionamento para minúsculas para facilitar a comparação
  const lowerCaseRelationships = relationships.map((rel) => rel.toLowerCase());
  const uniqueResults = new Map<string, any>(); // Guarda os resultados únicos pelo `label default key`

  // Verifica se um item corresponde à condição baseada no valor de returnMatching
  const matches = (item: any): boolean => {
    const labelKey = (item[labels.label_default_key] || "").toLowerCase();
    const relationshipKey = (
      item[labels.grau_de_parentesco] || ""
    ).toLowerCase();
    const isMatch = lowerCaseRelationships.some(
      (rel) => labelKey.includes(rel) || relationshipKey.includes(rel)
    );
    return returnMatching ? isMatch : !isMatch;
  };

  // Adiciona o item ao mapa se for o primeiro ou se possuir mais propriedades que o atual
  const addIfBetter = (item: any, key: string) => {
    if (
      !uniqueResults.has(key) ||
      Object.keys(item).length > Object.keys(uniqueResults.get(key)).length
    ) {
      uniqueResults.set(key, item);
    }
  };

  // Procura o objeto primário com `bookmark === 4`
  const primaryItem = findItemWithBookmark(personArray, 4);

  // Se existir e tiver um array `pessoa`, filtra os itens internos
  if (primaryItem && Array.isArray(primaryItem.pessoa)) {
    primaryItem.pessoa.forEach((item: any) => {
      const key = (item[labels.label_default_key] || "").toLowerCase();
      if (matches(item)) {
        addIfBetter(item, key);
      }
    });
  }

  // Filtra o array original, ignorando itens com `bookmark === 4`
  personArray.forEach((item: any) => {
    if (item.bookmark === 4) return;
    const key = (item[labels.label_default_key] || "").toLowerCase();
    if (matches(item)) {
      addIfBetter(item, key);
    }
  });

  return Array.from(uniqueResults.values());
}

export function findPersonByLabel(
  personArray: any[],
  label: string
): any | undefined {
  if (!Array.isArray(personArray)) return undefined;

  const person = personArray.find((item: any) => {
    const labelKey = (item[labels.label_default_key] || "").toLowerCase();
    return labelKey.includes(label.toLowerCase());
  });

  return person;
}

/**
 * Removes any keys from an object whose values are objects or arrays,
 * leaving only primitive properties (or null).
 * This function is intended to strip nested structures from the primary result.
 */
export function removeNestedProperties(data: any): any {
  if (data === null || typeof data !== "object") return data;
  const stripped: any = {};
  Object.keys(data).forEach((key) => {
    const value = data[key];
    if (
      value === null ||
      (typeof value !== "object" && typeof value !== "function")
    ) {
      stripped[key] = value;
    }
  });
  return stripped;
}

/**
 * Removes properties from an object or each object in an array that are listed in noRenderPropList.
 * This function performs a shallow removal on each object.
 * Returns the original data if it's not an object or if noRenderPropList is not an array.
 */
export function removeNoRenderProperties(
  data: any,
  noRenderPropList: string[]
): any {
  if (!data || typeof data !== "object" || !Array.isArray(noRenderPropList)) {
    return data;
  }

  if (Array.isArray(data)) {
    return data.map((item) => removeNoRenderProperties(item, noRenderPropList));
  }

  const result = { ...data };
  noRenderPropList.forEach((prop) => {
    if (prop in result) {
      delete result[prop];
    }
  });
  return result;
}

export function getObjectProps(
  targetData: any,
  arrayKey: string,
  propsToCopy: string[]
): any {
  // If targetData is an array, process each element individually.
  if (Array.isArray(targetData)) {
    return targetData.map((item) =>
      getObjectProps(item, arrayKey, propsToCopy)
    );
  }

  // If targetData is an object and has a nested array at arrayKey, copy properties.
  if (
    targetData &&
    typeof targetData === "object" &&
    Array.isArray(targetData[arrayKey]) &&
    targetData[arrayKey].length > 0
  ) {
    const childObject = targetData[arrayKey][0];
    propsToCopy.forEach((prop) => {
      if (childObject.hasOwnProperty(prop)) {
        targetData[prop] = childObject[prop];
      }
    });
  }
  return targetData;
}

export const getInitials = (name: string | undefined): string => {
  if (!name) return "";

  const nameString = name?.split(" ");
  const initials = !nameString
    ? ""
    : nameString.length === 1
      ? name?.substring(0, 2).toUpperCase()
      : (nameString[0][0] + nameString[nameString.length - 1][0]).toUpperCase();

  return initials;
};

export function getTypeIcon(
  type: string,
  size: number = 16
): React.ReactElement {
  const reportTypeIconMap: Record<string, (size?: number) => React.ReactElement> = {
    cpf: (size = 16) =>
      React.createElement(BsFilePersonFill, { size, className: "text-primary" }),
    cnpj: (size = 16) => React.createElement(BsBuilding, { size, className: "text-primary" }),
    telefone: (size = 16) =>
      React.createElement(MdLocalPhone, { size, className: "text-primary" }),
    email: (size = 16) => React.createElement(MdEmail, { size, className: "text-primary" }),
    relacoes: (size = 16) =>
      React.createElement(TbCirclesRelation, { size, className: "text-primary" }),
  };
  const creator = reportTypeIconMap[type.toLowerCase()];

  return creator
    ? creator(size)
    : React.createElement("div", { style: { width: size, height: size }, className: "bg-border" });
}

/**
 * Translates a property name to a user-friendly label using the translate_prop_to_label.json mapping.
 * The function performs case-insensitive matching by converting both the input and keys to lowercase.
 *
 * @param propName - The property name to translate
 * @returns The translated label if a match is found, otherwise returns the original string
 *
 * @example
 * // Returns "Nome Completo"
 * translatePropToLabel("full name");
 *
 * @example
 * // Returns "Nome Completo" (case-insensitive)
 * translatePropToLabel("FULL NAME");
 *
 * @example
 * // Returns "unknown_property" (no match found)
 * translatePropToLabel("unknown_property");
 */
export const translatePropToLabel = (propName: string): string => {
  if (!propName || typeof propName !== "string") return String(propName);

  const normalizedInput = propName.toLowerCase();

  // Create a lowercase map of keys for case-insensitive matching
  for (const key in TRANSLATED_LABELS) {
    if (key.toLowerCase() === normalizedInput) {
      return TRANSLATED_LABELS[key as keyof typeof TRANSLATED_LABELS];
    }
  }

  // Return original string if no match found
  return propName;
}

export const parseValue = (value: string): string => {
  if (!value || typeof value !== "string") return value;

  const normalizedInput = value.toLowerCase();

  for (const key in PARSED_VALUES) {
    if (key.toLowerCase() === normalizedInput) {
      return PARSED_VALUES[key as keyof typeof PARSED_VALUES];
    }
  }

  return value;
}

export function normalizeText(input: string): string {
  if (!input || typeof input !== "string") return "";

  return input
    .toLowerCase()
    .normalize('NFD')
    .replace(/\p{Diacritic}/gu, '')
    .replace(/\s+/g, ' ')
    .trim();
}


export function generateNgrams(text: string, n = 3): string[] {
  const grams: string[] = [];
  for (let i = 0; i + n <= text.length; i++) {
    grams.push(text.slice(i, i + n));
  }
  return grams;
}

function isStringArray(val: unknown): val is string[] {
  return Array.isArray(val) && val.every(item => typeof item === 'string');
}

function isRecordOfStringArrays(val: unknown): val is Record<string, string[]> {
  if (val == null || typeof val !== 'object' || Array.isArray(val)) return false;
  return Object.values(val).every(isStringArray);
}

/**
 * Dado um objeto cujos valores são:
 *   • string
 *   • string[]
 *   • Record<string, string[]>
 * retorna um objeto com os mesmos keys, mas cujos valores são arrays de n-grams.
 *
 * ex.:
 *   { report_name: "João Silva", tags: ["abc","def"], search_args: {cpf:["123"]} }
 * retorna:
 *   { report_name: ["joa", "o s", " si", "ilv", "va"], tags: ["abc", "ab", "bc", "def", "de", "ef"], search_args: ["123", "12", "23"] }
 */
export function extractNgrams<
  T extends Record<string, unknown>
>(input: T, n = 3): { [K in keyof T]?: string[] } {
  const out: Partial<Record<keyof T, string[]>> = {};

  for (const key of Object.keys(input) as Array<keyof T>) {
    const raw = input[key];

    let sources: string[] = [];
    if (typeof raw === 'string') { // quando é string simples
      sources = [raw];
    } else if (isStringArray(raw)) { // quando é array de strings
      sources = raw;
    } else if (isRecordOfStringArrays(raw)) { //quando é objeto de arrays de strings
      sources = Object.values(raw).flat();
    } else if (raw && typeof raw === 'object' && !Array.isArray(raw)) { // quando é objeto com valores simples
      const values = Object.values(raw as Record<string, unknown>);
      sources = values
        .filter(value => typeof value === 'string')
        .map(value => value as string);
    }

    if (sources.length) {
      out[key] = sources
        .map(str => normalizeText(str))
        .flatMap(norm => generateNgrams(norm, n));
    }
  }

  return out as { [K in keyof T]?: string[] };
}

export const formatIsoDate = (iso: string, onlyDate = false) => {
  if (!iso) return "";

  const hourConfig = onlyDate ? {} : { 
    hour: "2-digit" as const, 
    minute: "2-digit" as const, 
    hour12: false 
  };

  try {
    const date = new Date(iso);
    return date.toLocaleString("pt-BR", {
      day: "2-digit",
      month: "2-digit",
      year: "numeric",
      ...hourConfig,
      timeZone: "America/Sao_Paulo"
    });
  } catch (error) {
    console.log("iso: ", iso);
    console.error("Error formatting date:", error);
    return iso;
  }
}

export const getCurrentISODateWithMicroseconds = () => {
  const now = new Date();
  // Get the ISO string (YYYY-MM-DDTHH:mm:ss.sssZ)
  const isoString = now.toISOString();
  // Replace the Z with the timezone offset (+00:00)
  return isoString.replace('Z', '+00:00');
};