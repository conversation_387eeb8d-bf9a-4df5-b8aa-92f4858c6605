import React from 'react';
import {
  Accordion,
  AccordionItem,
  AccordionTrigger,
  AccordionContent,
} from './accordion';
import { Badge } from './badge';
import { getStrategyMap } from '../strategies/ReportStrategyFactory';
import { useNewStrategyMap } from '../strategies/NEW_ReportStrategyFactory';
import { useVisibleReportSections, useReportMode, useReportActions, useIsSaving } from '../../context/ReportContext';
import ProcessosSection from '../strategies/RenderProcessosStrategy';
import { REPORT_SECTIONS } from '../../config/constants';
import { LiaTrashRestoreAltSolid, LiaTrashAltSolid } from "react-icons/lia";

const NEW_ReportDetailComponent: React.FC = () => {
  const mode = useReportMode();
  const isTrashMode = mode === 'trash';
  const sections = useVisibleReportSections();
  const oldMap = getStrategyMap();
  const newMap = useNewStrategyMap();
  const actions = useReportActions();
  const isSaving = useIsSaving();
  console.log("[NEW_ReportDetailComponent] SEÇÕES: ", sections);

  const triggerClassName = "bg-accordion-header px-0 py-0 mb-4 data-[state=open]:mb-0 no-underline [&_div:first-child]:border-neutral-100 cursor-pointer";

  return (
    <Accordion type="multiple">
      {sections.map((section, idx) => {
        // Seção processos possui estratégia específica
        if (section.title === REPORT_SECTIONS.processos && !section.subsection) {
          const subsections = sections.filter(
            (s) => s.title === REPORT_SECTIONS.processos && !!s.subsection
          );
          return (
            <AccordionItem key={`section-${idx}`} value={`section-${idx}`} className='border-b-0'>
              <AccordionTrigger className={triggerClassName}>
                <div className="flex items-center gap-4">
                  <h3 className="font-mono text-lg uppercase">
                    {section.title}
                  </h3>
                  {!isTrashMode && (
                    <Badge
                      variant="secondary"
                      className="rounded-2xl px-4 py-0.5 bg-gray-400"
                    >
                      {section.data_count}
                    </Badge>
                  )}
                </div>
              </AccordionTrigger>
              <AccordionContent className="bg-card px-5 pb-0">
                <ProcessosSection
                  rootSection={section}
                  subSections={subsections}
                />
              </AccordionContent>
            </AccordionItem>
          );
        }

        // Pular subseções de processos
        if (
          section.title === REPORT_SECTIONS.processos &&
          !!section.subsection
        ) {
          return null;
        }

        // Seções com com estratégias novas (lixeira) tem prioridade
        const newStrategy = newMap[section.title];
        if (newStrategy) {
          const handleDeleteSection = () => {
            if (isSaving || !newStrategy.deleteSectionEntries) return;
            newStrategy.deleteSectionEntries();
          };

          return (
            <AccordionItem key={`section-${idx}`} value={`section-${idx}`} className='border-b-0'>
              <div className="group">
                <AccordionTrigger className={triggerClassName}>
                  <div className="flex items-center gap-4 w-full">
                    <h3 className="font-mono text-lg uppercase">
                      {section.title}
                    </h3>
                    <div className="flex items-center gap-2">
                      {!isTrashMode && (
                        <Badge
                          variant="secondary"
                          className="rounded-2xl px-4 py-0.5 bg-gray-400"
                        >
                          {section.data_count}
                        </Badge>
                      )}
                      {newStrategy.deleteSectionEntries && (
                        <span
                          onClick={(e) => {
                            e.stopPropagation();
                            if (!isSaving) handleDeleteSection();
                          }}
                          title={isSaving ? "Salvando alterações..." : (isTrashMode ? "Restaurar seção" : "Deletar seção")}
                          className={`
                            flex items-center justify-center
                            w-8 h-8
                            opacity-0
                            group-hover:opacity-100
                            cursor-pointer
                            ${isSaving ? 'pointer-events-none cursor-wait' : ''}
                            transition-opacity duration-200
                          `}
                        >
                          {isTrashMode ? (
                            <LiaTrashRestoreAltSolid
                              size={32}
                              color={isSaving ? "var(--muted-foreground)" : "var(--foreground)"}
                            />
                          ) : (
                            <LiaTrashAltSolid
                              size={32}
                              color={isSaving ? "var(--muted-foreground)" : "var(--primary)"}
                            />
                          )}
                        </span>
                      )}
                    </div>
                  </div>
                </AccordionTrigger>
              </div>
              <AccordionContent className="bg-card px-5">
                <div className="pt-5">
                  {newStrategy.render(section.data).map((el: React.ReactNode, j: number) => (
                    <div key={j}>{el}</div>
                  ))}
                </div>
              </AccordionContent>
            </AccordionItem>
          );
        }

        // Caso contrário, usar estratégia antiga
        if (!isTrashMode) {
          const PrinterStrategy = oldMap[section.title];
          if (PrinterStrategy) {
            // TODO - resolver isso de outra forma (talvez mudar a estratégia para receber a seção inteira?)
            // if (section.title === "Possíveis Contas em Sites") {
            //   return (
            //     <AccordionItem key={`section-${idx}`} value={`section-${idx}`} className=' border-b-0'>
            //       <AccordionTrigger className={triggerClassName}>
            //         <div className="flex items-center gap-4">
            //           <h3 className="font-mono text-lg uppercase">
            //             {section.title}
            //           </h3>
            //           <Badge
            //             variant="secondary"
            //             className="rounded-2xl px-4 py-0.5 bg-gray-400"
            //           >
            //             {section.data_count}
            //           </Badge>
            //         </div>
            //       </AccordionTrigger>
            //       <AccordionContent className="bg-card px-5">
            //         <div className="pt-5">
            //           {PrinterStrategy.render(section.data).map((el: React.ReactNode, j: number) => (
            //             <div key={j}>{el}</div>
            //           ))}
            //         </div>
            //       </AccordionContent>
            //     </AccordionItem>
            //   );
            // }
            
            return (
              <AccordionItem key={`section-${idx}`} value={`section-${idx}`} className='border-b-0'>
                <AccordionTrigger className={triggerClassName}>
                  <div className="flex items-center gap-4">
                    <h3 className="font-mono text-lg uppercase">
                      {section.title}
                    </h3>
                    <Badge
                      variant="secondary"
                      className="rounded-2xl px-4 py-0.5 bg-gray-400"
                    >
                      {section.data_count}
                    </Badge>
                  </div>
                </AccordionTrigger>
                <AccordionContent className="bg-card px-5">
                  {section.data.map((item: any, i: number) => (
                    <div key={i} className="pt-5">
                      {PrinterStrategy.render(item).map((el: React.ReactNode, j: number) => (
                        <div key={j}>{el}</div>
                      ))}
                    </div>
                  ))}
                </AccordionContent>
              </AccordionItem>
            );
          }
        }

        // Caso não encontre estratégia, pular
        console.warn(`Sem estratégia de renderização para seção "${section.title}"`);
        return null;
      })}
    </Accordion>
  );
};

export default NEW_ReportDetailComponent;